import {
  forwardRef,
  memo,
  ReactNode,
  useCallback,
  useEffect,
  useId,
  useImperativeHandle,
  useState,
} from 'react';

import { BlockNoteEditor, locales } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import {
  blockTypeSelectItems,
  DragHandleButton,
  FormattingToolbar,
  FormattingToolbarController,
  SideMenu,
  SideMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { debounce } from 'lodash';
import { toast } from 'sonner';

import { archivo } from '@/utils/fonts';

import './styles.scss';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

export type RichTextEditorProps = {
  id?: string;
  defaultValue?: string;
  bg?: 'white' | 'colored';
  maxHeight?: string;
  placeholder?: string;
  children?: ReactNode;
  onChange?: (text: string) => void;
  onImmediateChange?: (document: any) => void;
  onBlur?: () => void;
  className?: string;
  initialBlockType?:
    | 'paragraph'
    | 'bulletListItem'
    | 'numberedListItem'
    | 'checkListItem';
};

export type RichTextEditorRef = {
  editor: BlockNoteEditor;
};

const RichTextEditor = forwardRef(
  (
    {
      id,
      defaultValue = '',
      bg = 'colored',
      maxHeight = '',
      placeholder = 'Enter diagnosis',
      children,
      onChange = () => {},
      onImmediateChange = () => {},
      onBlur = () => {},
      className,
      initialBlockType = 'paragraph',
    }: RichTextEditorProps,
    ref
  ) => {
    const defaultId = useId();
    const localId = id || defaultId;

    const editor = useCreateBlockNote(
      {
        initialContent: [
          {
            id: 'placeholder-block',
            type: initialBlockType,
            content: '',
          },
        ],
        dictionary: {
          ...locales.en,
          placeholders: {
            ...locales.en.placeholders,
            default: placeholder,
          },
        },
      },
      [initialBlockType]
    );

    const allowedItems = [
      'paragraph',
      'bulletListItem',
      'numberedListItem',
      'checkListItem',
    ];
    const [blockTypeOptions] = useState(
      blockTypeSelectItems(editor.dictionary).filter((item) =>
        allowedItems.includes(item.type)
      )
    );

    const emitHTML = async () => {
      try {
        const htmlText = await editor.blocksToFullHTML(editor.document);
        onChange(htmlText);
      } catch {
        onChange('');
        toast.error('Error processing text input');
      }
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedHandleChange = useCallback(debounce(emitHTML, 300), [
      editor,
    ]);

    const handleChange = () => {
      debouncedHandleChange();
      onImmediateChange(editor.document);
    };

    // Function to clean up HTML content and remove empty blocks
    const cleanHTMLContent = useCallback((htmlContent: string): string => {
      if (!htmlContent) return '';

      // Parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');

      // Remove empty blocks (blocks with no text content)
      const blockOuters = doc.querySelectorAll('.bn-block-outer');
      blockOuters.forEach((blockOuter) => {
        const inlineContent = blockOuter.querySelector('.bn-inline-content');
        if (inlineContent && !inlineContent.textContent?.trim()) {
          blockOuter.remove();
        }
      });

      // If no blocks remain, return empty string
      const remainingBlocks = doc.querySelectorAll('.bn-block-outer');
      if (remainingBlocks.length === 0) {
        return '';
      }

      return doc.body.innerHTML;
    }, []);

    const loadHTMLToBlocks = useCallback(
      async (htmlContent: string) => {
        try {
          if (!htmlContent) {
            // If no content, clear the editor with a single empty paragraph
            editor.replaceBlocks(editor.document, [
              {
                id: 'empty-block',
                type: initialBlockType,
                content: '',
              },
            ]);
            return;
          }

          // Clean the HTML content before parsing
          const cleanedHTML = cleanHTMLContent(htmlContent);

          if (!cleanedHTML) {
            // If cleaned HTML is empty, set empty block
            editor.replaceBlocks(editor.document, [
              {
                id: 'empty-block',
                type: initialBlockType,
                content: '',
              },
            ]);
            return;
          }

          const blocks = await editor.tryParseHTMLToBlocks(cleanedHTML);
          // Always replace all blocks to prevent accumulation
          editor.replaceBlocks(editor.document, blocks);
        } catch {
          toast.error('Parsing summary failed');
        }
      },
      [editor, initialBlockType, cleanHTMLContent]
    );

    useImperativeHandle(ref, () => {
      return {
        editor,
      };
    }, [editor]);

    useEffect(() => {
      // Use a timeout to ensure the editor is ready and avoid debounce issues
      const timeoutId = setTimeout(() => {
        loadHTMLToBlocks(defaultValue || '');
      }, 50); // Reduced timeout for faster response

      return () => clearTimeout(timeoutId);
    }, [defaultValue, loadHTMLToBlocks]);

    return (
      <BlockNoteView
        id={localId}
        className={`
        rich-text-editor text-sm 
        ${bg} 
        ${className}
       
      `}
        style={{
          ...archivo.style,
          ...(maxHeight
            ? {
                maxHeight,
                overflow: 'auto',
              }
            : {}),
        }}
        editor={editor}
        sideMenu={false}
        slashMenu={false}
        formattingToolbar={false}
        onChange={handleChange}
        onBlur={onBlur}
      >
        <SideMenuController
          sideMenu={(props) => (
            <SideMenu {...props}>
              <DragHandleButton {...props} />
            </SideMenu>
          )}
        />
        <FormattingToolbarController
          formattingToolbar={() => (
            <FormattingToolbar blockTypeSelectItems={blockTypeOptions} />
          )}
        />

        {children}
      </BlockNoteView>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

const MemoRichTextEditor = memo(RichTextEditor);

export default MemoRichTextEditor;
