import {
  forwardRef,
  memo,
  ReactNode,
  useCallback,
  useEffect,
  useId,
  useImperativeHandle,
  useState,
} from 'react';

import { BlockNoteEditor, locales } from '@blocknote/core';
import { BlockNoteView } from '@blocknote/mantine';
import {
  blockTypeSelectItems,
  DragHandleButton,
  FormattingToolbar,
  FormattingToolbarController,
  SideMenu,
  SideMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import { debounce } from 'lodash';
import { toast } from 'sonner';

import { archivo } from '@/utils/fonts';

import './styles.scss';

import '@blocknote/core/fonts/inter.css';
import '@blocknote/mantine/style.css';

export type RichTextEditorProps = {
  id?: string;
  defaultValue?: string;
  bg?: 'white' | 'colored';
  maxHeight?: string;
  placeholder?: string;
  children?: ReactNode;
  onChange?: (text: string) => void;
  onImmediateChange?: (document: any) => void;
  onBlur?: () => void;
  className?: string;
  initialBlockType?:
    | 'paragraph'
    | 'bulletListItem'
    | 'numberedListItem'
    | 'checkListItem';
};

export type RichTextEditorRef = {
  editor: BlockNoteEditor;
};

const RichTextEditor = forwardRef(
  (
    {
      id,
      defaultValue = '',
      bg = 'colored',
      maxHeight = '',
      placeholder = 'Enter diagnosis',
      children,
      onChange = () => {},
      onImmediateChange = () => {},
      onBlur = () => {},
      className,
      initialBlockType = 'paragraph',
    }: RichTextEditorProps,
    ref
  ) => {
    const defaultId = useId();
    const localId = id || defaultId;

    const editor = useCreateBlockNote(
      {
        initialContent: [
          {
            id: 'placeholder-block',
            type: initialBlockType,
            content: '',
          },
        ],
        dictionary: {
          ...locales.en,
          placeholders: {
            ...locales.en.placeholders,
            default: placeholder,
          },
        },
      },
      [initialBlockType]
    );

    const allowedItems = [
      'paragraph',
      'bulletListItem',
      'numberedListItem',
      'checkListItem',
    ];
    const [blockTypeOptions] = useState(
      blockTypeSelectItems(editor.dictionary).filter((item) =>
        allowedItems.includes(item.type)
      )
    );

    // Function to clean HTML output for API payload
    const cleanHTMLOutput = useCallback((htmlContent: string): string => {
      if (!htmlContent) return '';

      // Parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');

      // Get all block content elements
      const blockContents = doc.querySelectorAll('.bn-block-content');
      const cleanedBlocks: string[] = [];

      blockContents.forEach((blockContent) => {
        const contentType = blockContent.getAttribute('data-content-type');
        const inlineContent = blockContent.querySelector('.bn-inline-content');

        if (inlineContent && inlineContent.textContent?.trim()) {
          const text = inlineContent.textContent.trim();

          // Create clean HTML based on content type
          switch (contentType) {
            case 'bulletListItem':
              cleanedBlocks.push(`<li>${text}</li>`);
              break;
            case 'numberedListItem':
              cleanedBlocks.push(`<li>${text}</li>`);
              break;
            case 'checkListItem':
              cleanedBlocks.push(`<li>${text}</li>`);
              break;
            default: // paragraph
              cleanedBlocks.push(`<p>${text}</p>`);
              break;
          }
        }
      });

      // If no content, return empty string
      if (cleanedBlocks.length === 0) {
        return '';
      }

      // Join blocks with minimal spacing
      return cleanedBlocks.join('');
    }, []);

    const emitHTML = async () => {
      try {
        const htmlText = await editor.blocksToFullHTML(editor.document);
        const cleanedHTML = cleanHTMLOutput(htmlText);
        onChange(cleanedHTML);
      } catch {
        onChange('');
        toast.error('Error processing text input');
      }
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const debouncedHandleChange = useCallback(debounce(emitHTML, 300), [
      editor,
    ]);

    const handleChange = () => {
      debouncedHandleChange();
      onImmediateChange(editor.document);
    };

    // Function to normalize HTML content for loading into editor
    const normalizeHTMLContent = useCallback((htmlContent: string): string => {
      if (!htmlContent) return '';

      // Parse the HTML
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlContent, 'text/html');

      // Check if it's already in BlockNote format
      const hasBlockNoteStructure = doc.querySelector('.bn-block-group');

      if (hasBlockNoteStructure) {
        // Remove empty blocks from BlockNote format
        const blockOuters = doc.querySelectorAll('.bn-block-outer');
        blockOuters.forEach((blockOuter) => {
          const inlineContent = blockOuter.querySelector('.bn-inline-content');
          if (inlineContent && !inlineContent.textContent?.trim()) {
            blockOuter.remove();
          }
        });

        // If no blocks remain, return empty string
        const remainingBlocks = doc.querySelectorAll('.bn-block-outer');
        if (remainingBlocks.length === 0) {
          return '';
        }

        return doc.body.innerHTML;
      } else {
        // Convert simple HTML (p, li tags) to BlockNote format
        const elements = doc.body.children;
        const blockNoteHTML: string[] = [];

        Array.from(elements).forEach((element, index) => {
          const text = element.textContent?.trim();
          if (text) {
            const blockId = `block-${Date.now()}-${index}`;
            let contentType = 'paragraph';

            if (element.tagName === 'LI') {
              contentType = 'bulletListItem';
            }

            blockNoteHTML.push(`
              <div class="bn-block-outer" data-node-type="blockOuter" data-id="${blockId}">
                <div class="bn-block" data-node-type="blockContainer" data-id="${blockId}">
                  <div class="bn-block-content" data-content-type="${contentType}">
                    <p class="bn-inline-content">${text}</p>
                  </div>
                </div>
              </div>
            `);
          }
        });

        if (blockNoteHTML.length === 0) {
          return '';
        }

        return `<div class="bn-block-group" data-node-type="blockGroup">${blockNoteHTML.join('')}</div>`;
      }
    }, []);

    const loadHTMLToBlocks = useCallback(
      async (htmlContent: string) => {
        try {
          if (!htmlContent) {
            // If no content, clear the editor with a single empty paragraph
            editor.replaceBlocks(editor.document, [
              {
                id: 'empty-block',
                type: initialBlockType,
                content: '',
              },
            ]);
            return;
          }

          // Normalize the HTML content before parsing
          const normalizedHTML = normalizeHTMLContent(htmlContent);

          if (!normalizedHTML) {
            // If normalized HTML is empty, set empty block
            editor.replaceBlocks(editor.document, [
              {
                id: 'empty-block',
                type: initialBlockType,
                content: '',
              },
            ]);
            return;
          }

          const blocks = await editor.tryParseHTMLToBlocks(normalizedHTML);
          // Always replace all blocks to prevent accumulation
          editor.replaceBlocks(editor.document, blocks);
        } catch {
          toast.error('Parsing summary failed');
        }
      },
      [editor, initialBlockType, normalizeHTMLContent]
    );

    useImperativeHandle(ref, () => {
      return {
        editor,
      };
    }, [editor]);

    useEffect(() => {
      loadHTMLToBlocks();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [defaultValue]);

    return (
      <BlockNoteView
        id={localId}
        className={`
        rich-text-editor text-sm 
        ${bg} 
        ${className}
       
      `}
        style={{
          ...archivo.style,
          ...(maxHeight
            ? {
                maxHeight,
                overflow: 'auto',
              }
            : {}),
        }}
        editor={editor}
        sideMenu={false}
        slashMenu={false}
        formattingToolbar={false}
        onChange={handleChange}
        onBlur={onBlur}
      >
        <SideMenuController
          sideMenu={(props) => (
            <SideMenu {...props}>
              <DragHandleButton {...props} />
            </SideMenu>
          )}
        />
        <FormattingToolbarController
          formattingToolbar={() => (
            <FormattingToolbar blockTypeSelectItems={blockTypeOptions} />
          )}
        />

        {children}
      </BlockNoteView>
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

const MemoRichTextEditor = memo(RichTextEditor);

export default MemoRichTextEditor;
